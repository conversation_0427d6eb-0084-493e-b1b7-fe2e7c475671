import { WeightedGameConfiguration } from "@betswirl/sdk-core"
import { useCallback, useEffect, useState } from "react"
import wheelArrow from "../../assets/game/wheel-arrow.svg"
import wheelDark from "../../assets/game/wheel-dark.svg"
import wheelLight from "../../assets/game/wheel-light.svg"
import { GameMultiplierDisplay } from "./shared/GameMultiplierDisplay"
import { GameControlsProps } from "./shared/types"

interface WheelGameControlsProps extends GameControlsProps {
  config: WeightedGameConfiguration
  winningMultiplier?: number
  theme?: "light" | "dark" | "system"
}

interface WheelSegment {
  index: number
  multiplier: number
  formattedMultiplier: string
  color: string
  startAngle: number
  endAngle: number
  weight: bigint
}

interface WheelProps {
  rotationAngle: number
  isSpinning: boolean
  multiplier: number
  hasCompletedSpin?: boolean
  theme?: "light" | "dark" | "system"
}

const SPIN_DURATION = 4000

const WHEEL_ANIMATION_CONFIG = {
  MIN_ROTATIONS: 5,
  MAX_ADDITIONAL_ROTATIONS: 3,
  MAX_RANDOM_OFFSET: 20,
} as const

export function createWheelSegments(config: WeightedGameConfiguration): WheelSegment[] {
  const totalSegments = config.multipliers.length
  const anglePerSegment = 360 / totalSegments

  return config.multipliers.map((multiplier, index) => {
    const startAngle = index * anglePerSegment
    const endAngle = (index + 1) * anglePerSegment
    const formattedMultiplier =
      multiplier === 0n ? "0.00x" : `${(Number(multiplier) / 10000).toFixed(2)}x`

    return {
      index,
      multiplier: Number(multiplier),
      formattedMultiplier,
      color: config.colors?.[index] || "#29384C",
      startAngle,
      endAngle,
      weight: config.weights[index] || 1n,
    }
  })
}

function getTargetAngleForMultiplier(segments: WheelSegment[], winningMultiplier: number): number {
  const winningSegments = segments.filter((segment) => segment.multiplier === winningMultiplier)
  if (winningSegments.length === 0) {
    return 0
  }

  const randomSegment = winningSegments[Math.floor(Math.random() * winningSegments.length)]
  const fullRotations =
    WHEEL_ANIMATION_CONFIG.MIN_ROTATIONS +
    Math.floor(Math.random() * WHEEL_ANIMATION_CONFIG.MAX_ADDITIONAL_ROTATIONS)
  const targetAngle = fullRotations * 360 - randomSegment.startAngle

  return targetAngle
}

function Wheel({
  rotationAngle,
  isSpinning,
  multiplier,
  hasCompletedSpin = false,
  theme = "light",
}: WheelProps) {
  const [currentAngle, setCurrentAngle] = useState(0)
  const shouldShowMultiplier = hasCompletedSpin && !isSpinning

  useEffect(() => {
    if (rotationAngle !== currentAngle) {
      setCurrentAngle(rotationAngle)
    }
  }, [rotationAngle, currentAngle])

  // Choose the correct wheel SVG based on theme
  const wheelSrc = theme === "dark" ? wheelDark : wheelLight

  return (
    <>
      <div className="relative w-[192px] h-[148px] mx-auto">
        <div
          className={`absolute inset-0 flex items-center justify-center top-[12px] ${
            isSpinning ? "wheel-spinning" : ""
          }`}
          style={{ transform: `rotate(${currentAngle}deg)` }}
        >
          <img src={wheelSrc} alt="Wheel colors" className="w-full h-full object-contain" />
        </div>
        {shouldShowMultiplier && (
          <GameMultiplierDisplay
            multiplier={multiplier}
            className="absolute text-wheel-multiplier-text top-[80px] text-[18px]"
          />
        )}
      </div>
      <img src={wheelArrow} alt="Wheel arrow" className="absolute" />
    </>
  )
}

export function WheelGameControls({
  config,
  winningMultiplier,
  multiplier,
  theme = "light",
}: WheelGameControlsProps) {
  const [segments, setSegments] = useState<WheelSegment[]>([])
  const [rotationAngle, setRotationAngle] = useState(0)
  const [isSpinning, setIsSpinning] = useState(false)
  const [hasResult, setHasResult] = useState(false)
  const [displayedWinningMultiplier, setDisplayedWinningMultiplier] = useState<number | undefined>()

  const resetWheelState = useCallback(() => {
    setHasResult(false)
    setDisplayedWinningMultiplier(undefined)
  }, [])

  const getDisplayMultiplier = (): number => {
    return displayedWinningMultiplier !== undefined
      ? displayedWinningMultiplier / 10000
      : multiplier
  }

  const isMultiplierWinning = (itemMultiplier: number): boolean => {
    return hasResult && displayedWinningMultiplier === itemMultiplier
  }

  useEffect(() => {
    const wheelSegments = createWheelSegments(config)
    setSegments(wheelSegments)
  }, [config])

  useEffect(() => {
    if (winningMultiplier === undefined) {
      resetWheelState()
    } else if (winningMultiplier !== displayedWinningMultiplier && segments.length > 0) {
      resetWheelState()
      setIsSpinning(true)
      const targetAngle = getTargetAngleForMultiplier(segments, winningMultiplier)
      setRotationAngle(targetAngle)

      const timer = setTimeout(() => {
        setIsSpinning(false)
        setDisplayedWinningMultiplier(winningMultiplier)
        setHasResult(true)
      }, SPIN_DURATION)

      return () => clearTimeout(timer)
    }
  }, [winningMultiplier, segments, displayedWinningMultiplier, resetWheelState])

  const uniqueMultipliers = segments
    .reduce(
      (acc, segment) => {
        const existing = acc.find((item) => item.multiplier === segment.multiplier)
        if (!existing) {
          acc.push({
            multiplier: segment.multiplier,
            formattedMultiplier: segment.formattedMultiplier,
            color: segment.color,
          })
        }
        return acc
      },
      [] as Array<{ multiplier: number; formattedMultiplier: string; color: string }>,
    )
    .sort((a, b) => a.multiplier - b.multiplier)

  return (
    <div className="flex flex-col items-center gap-[8px] absolute top-[8px] left-1/2 transform -translate-x-1/2 w-full max-w-lg px-4">
      <Wheel
        rotationAngle={rotationAngle}
        isSpinning={isSpinning}
        multiplier={getDisplayMultiplier()}
        hasCompletedSpin={hasResult}
        theme={theme}
      />

      <div className="flex flex-wrap justify-center gap-[6px] w-full">
        {uniqueMultipliers.map((item) => {
          const isWinning = isMultiplierWinning(item.multiplier)
          return (
            <div
              key={item.multiplier}
              className={`flex h-[24px] w-[49px] items-center justify-center rounded-[2px] backdrop-blur-sm bg-wheel-multiplier-bg text-wheel-multiplier-text wheel-multiplier-item ${
                isWinning ? "wheel-multiplier-winning" : ""
              }`}
              style={
                {
                  "--wheel-color": item.color,
                } as React.CSSProperties
              }
            >
              <span className="text-xs font-bold">{item.formattedMultiplier}</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}
